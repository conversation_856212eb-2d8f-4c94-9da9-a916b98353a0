import { useCallback } from 'react'
import opentype from 'opentype.js'
import { TextOverlay } from '@clipnest/remotion-shared/types'
import { GreedyTextWrapper, TextWidthCacheManager } from '../utils/greed-text'

export type TextRenderInfo = {
  x: number
  y: number
  scale: number
  fontSize: number
  minHeight: number
  minWidth: number
  letterSpacing: number
  lineSpacing: number

  wrappedLines: { content: string, width: number }[]
  lineHeight: number
  totalHeight: number
}

// 可选的文本样式更新参数
export type TextPropOverrides = {
  content?: string
  width?: number,
  fontSize?: number
  letterSpacing?: number
  lineSpacing?: number
}

type TextRenderInfoCalculateFunc = (
  overrides?: TextPropOverrides
) => TextRenderInfo

const textWidthCache = TextWidthCacheManager.getInstance()

// 行高因子
const LINE_HEIGHT_FACTOR = 1.0

const getTextWidth = (
  font: opentype.Font,
  fontSrc: string,
  content: string,
  fontSize: number,
  letterSpacing: number = 0
): number => {
  if (!content) {
    return 1
  }

  // 检查缓存（包含字间距参数）
  const cachedWidth = textWidthCache.getCachedWidth(fontSrc, content, fontSize, letterSpacing)
  if (cachedWidth !== null) {
    return cachedWidth
  }

  try {
    // 计算基础文本宽度
    const baseTextWidth = font.getAdvanceWidth(content, fontSize)

    // 添加字间距：字符数量减1乘以字间距（最后一个字符后不加字间距）
    const letterSpacingWidth = Math.max(0, content.length - 1) * letterSpacing
    const totalWidth = baseTextWidth + letterSpacingWidth

    textWidthCache.setCachedWidth(fontSrc, content, fontSize, totalWidth, letterSpacing)
    return totalWidth
  } catch (error) {
    console.error('[增强文本渲染器] 计算文本宽度失败:', error)
    return 1
  }
}

function wrapTextWithGreedy(
  font: opentype.Font,
  fontSrc: string,
  content: string,
  containerWidth: number,
  fontSize: number,
  letterSpacing: number = 0,
  respectWordBoundary: boolean = false
): { content: string, width: number }[] {
  try {
    const wrapper = new GreedyTextWrapper(font, fontSize, containerWidth, letterSpacing)
    const lines = wrapper.wrapText(content, respectWordBoundary)

    // 为每行计算宽度并返回对象数组
    return lines.map(line => ({
      content: line,
      width: getTextWidth(font, fontSrc, line, fontSize, letterSpacing)
    }))
  } catch (error) {
    console.error('[增强文本渲染器] 优化贪心换行失败:', error)
    return [{
      content: content,
      width: getTextWidth(font, fontSrc, content, fontSize, letterSpacing)
    }]
  }
}

export function calculateTextWrapInfo(
  font: opentype.Font,
  overlay: TextOverlay,
  overrides?: TextPropOverrides
) {
  const {
    width = overlay.width,
    content = overlay.content,
    fontSize = overlay.styles.fontSize ?? 0,
    lineSpacing = overlay.styles.lineSpacing ?? 0,
    letterSpacing = overlay.styles.letterSpacing ?? 0
  } = overrides || {}

  // 直接使用数值类型的字间距和行间距，提供默认值，支持样式覆盖
  const lineHeight = fontSize * LINE_HEIGHT_FACTOR

  // 计算实际行间距像素值：行间距倍数 * 字体大小
  const lineSpacingPixels = lineSpacing * fontSize

  const wrappedLines = wrapTextWithGreedy(
    font,
    overlay.src,
    content,
    width,
    fontSize,
    letterSpacing,
    true
  )

  // 计算总高度：行数 * 行高 + (行数 - 1) * 实际行间距
  const lineCount = wrappedLines.length
  const totalHeight = (lineCount * lineHeight) + (Math.max(0, lineCount - 1) * lineSpacingPixels)

  // 直接从预计算的宽度中获取最大值，避免重复计算
  const maxLineWidth = Math.max(...wrappedLines.map(line => line.width))

  return {
    wrappedLines,
    lineHeight,
    totalHeight,
    maxLineWidth
  }
}

export const useCalculateTextRenderInfo = (
  currentFont: opentype.Font | null,
  overlay: TextOverlay | null,
) => {
  const buildCalcTextRenderInfoFunction = useCallback(
    (): (null | TextRenderInfoCalculateFunc) => {
      if (!overlay || !currentFont) {
        return null
      }

      return (overrides?: TextPropOverrides): TextRenderInfo => {
        const {
          width = overlay.width,
          fontSize = overlay.styles.fontSize,
          lineSpacing = overlay.styles.lineSpacing ?? 0,
          letterSpacing = overlay.styles.letterSpacing ?? 0,
        } = overrides || {}

        const centerX = width / 2
        const centerY = overlay.height / 2
        const textAlign = overlay.styles.textAlign

        // 计算实际行间距像素值：行间距倍数 * 字体大小
        const actualLineSpacing = lineSpacing * fontSize

        const { totalHeight, lineHeight, wrappedLines, maxLineWidth } = calculateTextWrapInfo(
          currentFont,
          overlay,
          overrides
        )

        // 根据 textAlign 计算水平位置
        const getHorizontalPosition = () => {
          switch (textAlign) {
            case 'left':
              return 0
            case 'right':
              return width
            case 'center':
            default:
              return centerX
          }
        }

        // 对于多行文本，不需要缩放，直接使用原始字体大小
        const scale = 1

        const x = getHorizontalPosition()
        // 计算多行文本的起始位置（垂直居中）
        const y = centerY - totalHeight / 2 + lineHeight / 2

        return {
          x,
          y,
          scale,
          fontSize,
          letterSpacing,
          wrappedLines,
          lineHeight,
          totalHeight,
          minHeight: totalHeight,
          minWidth: maxLineWidth,
          lineSpacing: actualLineSpacing,
        }
      }
    },
    [overlay, currentFont]
  )

  return {
    buildCalcTextRenderInfoFunction
  }
}
