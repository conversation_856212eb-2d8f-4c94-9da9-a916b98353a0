import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { TextOverlay } from '@clipnest/remotion-shared/types'
import * as opentype from 'opentype.js'
import { TextToSvgConvertor } from '../../utils/text-to-svg'
import { continueRender, delayRender } from 'remotion'
import { TextRenderInfo, useCalculateTextRenderInfo } from '../../hooks/useCalculateTextRenderInfo'

const ADDITIONAL_BOX_HEIGHT = 0

interface CloudTextRendererProps {
  overlay: TextOverlay
  containerStyle: React.CSSProperties
}

const TextRenderSvgPart: React.FC<{
  overlay: TextOverlay, font: opentype.Font, renderInfo: TextRenderInfo
}> = ({ overlay, font, renderInfo }) => {
  const { styles: textStyle } = overlay

  const hasStroke = Boolean(
    textStyle.strokeEnabled
    && overlay.styles.strokeWidth
    && overlay.styles.strokeColor
  )

  const hasShadow = Boolean(
    textStyle.shadowEnabled
    && overlay.styles.shadowDistance
    && overlay.styles.shadowColor
  )

  if (!hasStroke && !hasShadow) {
    return null
  }

  const svgRef = useRef<SVGSVGElement>(null)

  // 确保有有效的尺寸
  if (overlay.width === 0) {
    return null
  }

  const generateSVGFilters = useCallback(
    () => {
      const filters: React.ReactElement[] = []

      if (hasShadow) {
        const distance = overlay.styles.shadowDistance ? overlay.styles.shadowDistance / 2 : 0
        const angle = overlay.styles.shadowAngle || 45
        const blur = overlay.styles.shadowBlur || 2
        const color = overlay.styles.shadowColor || '#000000'
        const opacity = overlay.styles.shadowOpacity || 0.5

        if (distance > 0) {
          const angleRad = (angle * Math.PI) / 180
          const offsetX = Math.cos(angleRad) * distance
          const offsetY = Math.sin(angleRad) * distance

          filters.push(
            <filter
              key="shadow"
              id={`shadow-${overlay.id}`}
              x="-100%"
              y="-100%"
              width="300%"
              height="300%"
              filterUnits="userSpaceOnUse"
              colorInterpolationFilters="sRGB"
            >
              <feDropShadow
                dx={offsetX}
                dy={offsetY}
                stdDeviation={blur}
                floodColor={color}
                floodOpacity={opacity}
              />
            </filter>
          )
        }
      }

      return filters
    },
    [hasShadow, overlay.styles, overlay.id]
  )

  const renderOpentypeSVGPath = useCallback(
    () => {
      try {
        const strokeWidth = overlay.styles.strokeWidth ?? 0
        const strokeColor = overlay.styles.strokeColor || '#000000'
        const filterId = hasShadow ? `shadow-${overlay.id}` : undefined

        const textToSvg = new TextToSvgConvertor(font, font)
        const paths: React.ReactElement[] = []

        const {
          x, y, fontSize, wrappedLines, lineHeight, lineSpacing
        } = renderInfo

        // 为每一行生成 SVG 路径
        wrappedLines.forEach((lineInfo, index) => {
          if (!lineInfo.content.trim()) return // 跳过空行

          const { d } = textToSvg.getD(lineInfo.content, {
            x: 0,
            y: 0,
            fontSize: fontSize,
            anchor: 'left middle',
            letterSpacing: (overlay.styles.letterSpacing ?? 0) / fontSize // 转换为相对值
          })

          if (!d) return

          // 计算每行的位置：基础位置 + 行索引 * (行高 + 行间距)
          const lineY = y + (index * (lineHeight + lineSpacing))

          // 根据 textAlign 计算水平位置
          const textAlign = textStyle.textAlign || 'center'
          const lineWidth = lineInfo.width // 直接使用预计算的宽度

          let lineX: number
          switch (textAlign) {
            case 'left':
              lineX = x
              break
            case 'right':
              lineX = x - lineWidth
              break
            case 'center':
            default:
              lineX = x - lineWidth / 2
              break
          }

          // 构建变换字符串，包含位置和样式变换
          let transform = `translate(${lineX}, ${lineY})`

          // 添加斜体变换
          if (textStyle.fontStyle === 'italic') {
            transform += ' skewX(-12)' // 向左倾斜 12 度来模拟斜体
          }

          // 渲染主要文本路径
          paths.push(
            <path
              key={`line-${index}`}
              d={d}
              fill="none"
              stroke={hasStroke ? strokeColor : 'none'}
              strokeWidth={strokeWidth}
              strokeLinejoin="round"
              strokeLinecap="round"
              filter={filterId ? `url(#${filterId})` : undefined}
              transform={transform}
              style={{
                vectorEffect: 'non-scaling-stroke'
              }}
            />
          )

          // 如果是加粗，添加额外的路径来增加厚度
          if (textStyle.fontWeight === 'bold') {
            const boldOffset = Math.max(0.5, fontSize * 0.01)

            // 添加多个偏移的路径来模拟加粗效果
            const boldTransforms = [
              `translate(${lineX + boldOffset}, ${lineY})`,
              `translate(${lineX}, ${lineY + boldOffset})`,
              `translate(${lineX + boldOffset}, ${lineY + boldOffset})`
            ]

            boldTransforms.forEach((boldTransform, boldIndex) => {
              let finalBoldTransform = boldTransform
              if (textStyle.fontStyle === 'italic') {
                finalBoldTransform += ' skewX(-12)'
              }

              paths.push(
                <path
                  key={`line-bold-${index}-${boldIndex}`}
                  d={d}
                  fill="none"
                  stroke={hasStroke ? strokeColor : 'none'}
                  strokeWidth={strokeWidth}
                  strokeLinejoin="round"
                  strokeLinecap="round"
                  filter={filterId ? `url(#${filterId})` : undefined}
                  transform={finalBoldTransform}
                  style={{
                    vectorEffect: 'non-scaling-stroke'
                  }}
                />
              )
            })
          }

          // 添加下划线
          if (textStyle.underlineEnabled) {
            // 由于 SVG anchor 是 'left middle'，lineY 是文字中心
            // 下划线应该在文字底部，所以需要加上字体大小的一半，再稍微向下偏移
            const underlineY = lineY + fontSize * 0.5 + fontSize * 0.1
            const underlineThickness = Math.max(1, fontSize * 0.05) // 下划线厚度

            paths.push(
              <line
                key={`underline-${index}`}
                x1={lineX}
                y1={underlineY}
                x2={lineX + lineWidth}
                y2={underlineY}
                stroke={hasStroke ? strokeColor : overlay.styles.color || '#ffffff'}
                strokeWidth={underlineThickness}
                strokeLinecap="round"
                filter={filterId ? `url(#${filterId})` : undefined}
              />
            )
          }
        })

        return <g>{paths}</g>
      } catch (error) {
        console.error('[增强文本渲染器] SVG路径渲染失败:', error)
        return null
      }
    },
    [overlay, hasStroke, hasShadow, textStyle, renderInfo]
  )

  const svgFilters = generateSVGFilters()
  const svgPath = renderOpentypeSVGPath()

  // 计算 SVG 需要的高度
  const requiredHeight = Math.max(overlay.height, renderInfo.totalHeight + ADDITIONAL_BOX_HEIGHT)

  return (
    <svg
      ref={svgRef}
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        zIndex: 1,
        pointerEvents: 'none'
      }}
      viewBox={`0 0 ${overlay.width} ${requiredHeight}`}
      preserveAspectRatio="xMidYMid meet"
    >
      <defs>
        {svgFilters}
      </defs>
      {svgPath}
    </svg>
  )
}

const TextRendererCanvasPart: React.FC<{
  overlay: TextOverlay, renderInfo: TextRenderInfo
}> = ({ overlay, renderInfo }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null)

  /**
   * 渲染带字间距的文本行
   * @param ctx Canvas 2D 上下文
   * @param text 要渲染的文本
   * @param x 起始 X 坐标
   * @param y Y 坐标
   * @param letterSpacing 字间距
   * @param textAlign 文本对齐方式
   * @param lineWidth 行的总宽度（用于对齐计算）
   */
  const renderTextWithLetterSpacing = useCallback((
    ctx: CanvasRenderingContext2D,
    text: string,
    x: number,
    y: number,
    letterSpacing: number,
    textAlign: string,
    lineWidth: number
  ) => {
    // 计算起始位置，考虑文本对齐
    let startX = x
    if (textAlign === 'center') {
      startX = x - lineWidth / 2
    } else if (textAlign === 'right') {
      startX = x - lineWidth
    }

    if (letterSpacing === 0) {
      // 如果没有字间距，使用原生渲染，但仍需考虑对齐
      ctx.fillText(text, startX, y)
      return
    }

    // 逐字符渲染
    let currentX = startX
    for (let i = 0; i < text.length; i++) {
      const char = text[i]
      ctx.fillText(char, currentX, y)

      // 计算字符宽度并添加字间距
      const charWidth = ctx.measureText(char).width
      currentX += charWidth

      // 在字符之间添加字间距（最后一个字符后不加）
      if (i < text.length - 1) {
        currentX += letterSpacing
      }
    }
  }, [])

  // Canvas 渲染效果（支持多行文本）
  useEffect(
    () => {
      if (!canvasRef.current || overlay.width === 0) {
        return
      }

      const { styles: textStyle } = overlay
      const canvas = canvasRef.current
      if (!canvas) return

      const ctx = canvas.getContext('2d')
      if (!ctx) return

      const {
        x, y, totalHeight, wrappedLines, lineHeight, letterSpacing, lineSpacing
      } = renderInfo

      // 动态调整 Canvas 高度以适应文本内容
      const requiredHeight = Math.max(overlay.height, totalHeight + ADDITIONAL_BOX_HEIGHT) // 添加一些padding

      // 设置 Canvas 尺寸
      canvas.width = overlay.width * window.devicePixelRatio
      canvas.height = requiredHeight * window.devicePixelRatio
      canvas.style.width = `${overlay.width}px`
      canvas.style.height = `${requiredHeight}px`

      // 缩放上下文以适应高DPI显示
      ctx.scale(window.devicePixelRatio, window.devicePixelRatio)

      // 清除画布
      ctx.clearRect(0, 0, overlay.width, requiredHeight)

      // 构建字体字符串（不包含 fontWeight 和 fontStyle，这两个属性将由其他方式实现）
      const fallbackFontFamily = textStyle.fontFamily || 'Arial'
      const fontString = `${overlay.styles.fontSize}px "${overlay.styles.fontFamily}", ${fallbackFontFamily}, sans-serif`

      // 设置文字样式
      ctx.font = fontString
      ctx.fillStyle = overlay.styles.color || '#ffffff'
      ctx.textBaseline = 'middle'

      // 注意：不设置 ctx.textAlign，因为我们会在 renderTextWithLetterSpacing 中手动处理对齐

      // 逐行渲染文本
      wrappedLines.forEach((lineInfo, index) => {
        // 计算行位置：基础位置 + 行索引 * (行高 + 行间距)
        const lineY = y + (index * (lineHeight + lineSpacing))

        ctx.save()

        // 计算斜体偏移补偿
        let italicOffsetX = 0
        if (textStyle.fontStyle === 'italic') {
          // 计算斜体变换导致的水平偏移
          // skew 变换会导致文本在 Y 轴方向上的位置影响 X 轴位置
          const skewFactor = -0.2
          italicOffsetX = lineY * skewFactor

          // 应用斜体变换
          ctx.transform(1, 0, skewFactor, 1, 0, 0)
        }

        // 计算补偿后的 X 坐标
        const adjustedX = x - italicOffsetX

        // 使用支持字间距的渲染函数
        const textAlign = textStyle.textAlign || 'center'
        renderTextWithLetterSpacing(
          ctx,
          lineInfo.content,
          adjustedX,
          lineY,
          letterSpacing,
          textAlign,
          lineInfo.width
        )

        // 如果是加粗，通过多次渲染来模拟加粗效果
        if (textStyle.fontWeight === 'bold') {
          const boldOffset = Math.max(0.5, overlay.styles.fontSize * 0.01)

          // 加粗效果也需要使用字间距渲染
          renderTextWithLetterSpacing(
            ctx,
            lineInfo.content,
            adjustedX + boldOffset,
            lineY,
            letterSpacing,
            textAlign,
            lineInfo.width
          )
          renderTextWithLetterSpacing(
            ctx,
            lineInfo.content,
            adjustedX,
            lineY + boldOffset,
            letterSpacing,
            textAlign,
            lineInfo.width
          )
          renderTextWithLetterSpacing(
            ctx,
            lineInfo.content,
            adjustedX + boldOffset,
            lineY + boldOffset,
            letterSpacing,
            textAlign,
            lineInfo.width
          )
        }

        ctx.restore()
      })
    },
    [overlay, renderInfo, renderTextWithLetterSpacing]
  )

  return (
    <canvas
      ref={canvasRef}
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        zIndex: 2,
        pointerEvents: 'none'
      }}
    />
  )
}

const OpentypeFontBasedTextRenderer: React.FC<{
  font: opentype.Font,
  overlay: TextOverlay,
  containerStyle: any
}> = ({ font, overlay, containerStyle }) => {
  const { styles: textStyle } = overlay

  const hasBubbleBackground = !!textStyle.backgroundImage
  const shouldUseBubblePosition = hasBubbleBackground && !!overlay.styles.bubbleTextRect

  const { buildCalcTextRenderInfoFunction } = useCalculateTextRenderInfo(font, overlay)

  const calculateTextRenderInfo = buildCalcTextRenderInfoFunction()!

  const textRenderInfo = useMemo(
    () => calculateTextRenderInfo(),
    [calculateTextRenderInfo]
  )

  // 增强容器样式，确保背景图片正确显示
  const finalContainerStyle: React.CSSProperties = {
    ...containerStyle,
    backgroundImage: hasBubbleBackground ? `url(${overlay.styles.backgroundImage})` : undefined,
    backgroundSize: 'contain',
    backgroundRepeat: 'no-repeat',
    backgroundPosition: 'center'
  }

  // 计算气泡图中文字的位置和样式
  const bubbleTextStyle = useMemo((): React.CSSProperties => {
    if (!hasBubbleBackground) {
      return {}
    }

    return {
      position: 'absolute',
      left: '50%',
      top: '50%',
      transform: 'translate(-50%, -50%)',
      width: '100%',
      height: '60%',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      boxSizing: 'border-box'
    }
  }, [hasBubbleBackground])

  const renderDualLayerContent = () => (
    <div style={{
      position: 'relative',
      width: '100%',
      height: overlay.height,
      backgroundColor: textStyle.backgroundColor,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      opacity: overlay.styles.textOpacity ?? 1
    }}
    >
      {/* SVG 层 - 底层，负责轮廓和阴影效果 */}
      <TextRenderSvgPart overlay={overlay} renderInfo={textRenderInfo} font={font} />

      {/* Canvas 层 - 上层，负责文字填充 */}
      <TextRendererCanvasPart overlay={overlay} renderInfo={textRenderInfo} />
    </div>
  )

  const renderMainContent = () => {
    if (shouldUseBubblePosition) {
      return (
        <div style={bubbleTextStyle}>
          {renderDualLayerContent()}
        </div>
      )
    }

    return renderDualLayerContent()
  }

  const finalHeight = Math.max(overlay.height, textRenderInfo.totalHeight + ADDITIONAL_BOX_HEIGHT)

  return (
    <div
      style={{
        ...finalContainerStyle,
        position: 'relative',
        overflow: 'hidden',
        height: `${finalHeight}px`
      }}
    >
      {renderMainContent()}
    </div>
  )
}

const CloudTextRendererComponent: React.FC<CloudTextRendererProps> = ({
  overlay,
  containerStyle,
}) => {
  const [handle] = useState(() => delayRender())

  const [fontInfo, setFontInfo] = useState<{ font: opentype.Font, fontFamily: string } | null>(null)

  const loadFont = async () => {
    const fontSrc = overlay.src
    const fontFamily = overlay.styles.fontFamily
    if (!fontSrc) {
      return null
    }

    const font = await opentype.load(fontSrc)

    try {
      const buildFontUrl = (path: string) => {
        if (path.startsWith('http://') || path.startsWith('https://')) {
          return path
        }

        const normalizedPath = path.replace(/\\/g, '/')
        if (normalizedPath.startsWith('/')) {
          return `file://${normalizedPath}`
        }

        return `file:///${normalizedPath}`
      }

      const fontUrl = buildFontUrl(fontSrc)

      const existingFontFace = Array.from(document.fonts).find(
        face => face.family === fontFamily
      )

      if (!existingFontFace) {
        const fontFace = new FontFace(fontFamily, `url("${fontUrl}")`)
        await fontFace.load()
        document.fonts.add(fontFace)
        console.log(`[CLOUD] 字体已加载到 DOM: ${fontFamily}`)
      }
    } catch (domError) {
      console.warn('[CLOUD] DOM 字体加载失败，但 opentype.js 加载成功:', domError)
    }

    setFontInfo({
      font,
      fontFamily
    })
  }

  // useEffect(() => {
  //   (async () => {
  //     // 等待字体加载完成
  //     if (fontInfo?.fontFamily) {
  //       try {
  //         await document.fonts.ready
  //         // 额外检查特定字体是否已加载
  //         await document.fonts.load(`${overlay.styles.fontSize}px "${fontInfo.fontFamily}"`)
  //       } catch (error) {
  //         console.warn('[CLOUD] 等待字体加载失败:', error)
  //       }
  //     }
  //   })()
  // }, [])

  useEffect(() => {
    if (fontInfo) continueRender(handle)
  }, [fontInfo])

  useEffect(() => {
    void loadFont()
  }, [overlay.src, overlay.styles.fontFamily]) // 监听字体源和字体族名称的变化

  if (!fontInfo) return null

  return (
    <OpentypeFontBasedTextRenderer
      font={fontInfo.font}
      overlay={overlay}
      containerStyle={containerStyle}
    />
  )
}

export const CloudTextRenderer = React.memo(
  CloudTextRendererComponent,
  (prevProps, nextProps) => {
    const basicPropsEqual = (
      prevProps.overlay.id === nextProps.overlay.id &&
      prevProps.overlay.content === nextProps.overlay.content &&
      prevProps.overlay.src === nextProps.overlay.src &&
      prevProps.overlay.width === nextProps.overlay.width &&
      prevProps.overlay.left === nextProps.overlay.left &&
      prevProps.overlay.top === nextProps.overlay.top &&
      prevProps.overlay.rotation === nextProps.overlay.rotation
    )

    const stylesEqual = JSON.stringify(prevProps.overlay.styles) === JSON.stringify(nextProps.overlay.styles)

    const containerStyleEqual = JSON.stringify(prevProps.containerStyle) === JSON.stringify(nextProps.containerStyle)

    return basicPropsEqual && stylesEqual && containerStyleEqual
  }
)
